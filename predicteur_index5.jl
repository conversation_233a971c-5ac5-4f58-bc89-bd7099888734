"""
PRÉDICTEUR INDEX5 - DÉTERMINATION DES VALEURS POSSIBLES À LA MAIN N+1
=====================================================================

Programme qui détermine les 6 valeurs possibles d'INDEX5 à la main n+1
basé sur les règles de transition d'INDEX1 selon INDEX2.
Les possibilités TIE à l'INDEX3 sont exclues.

RÈGLES DE TRANSITION INDEX1 :
- Si INDEX1 = 0 et INDEX2 = C à la main n → INDEX1 = 1 à la main n+1
- Si INDEX1 = 1 et INDEX2 = C à la main n → INDEX1 = 0 à la main n+1
- Si INDEX1 = 0 et INDEX2 = A à la main n → INDEX1 = 0 à la main n+1
- Si INDEX1 = 1 et INDEX2 = A à la main n → INDEX1 = 1 à la main n+1
- Si INDEX1 = 0 et INDEX2 = B à la main n → INDEX1 = 0 à la main n+1
- Si INDEX1 = 1 et INDEX2 = B à la main n → INDEX1 = 1 à la main n+1

INDEX5 = INDEX1_INDEX2_INDEX3 (avec INDEX3 ∈ {BANKER, PLAYER})
"""

using JSON
using Printf
using Dates

# ═══════════════════════════════════════════════════════════════════════════════
# STRUCTURES ET TYPES
# ═══════════════════════════════════════════════════════════════════════════════

"""
    MainData

Structure pour stocker les données d'une main.
"""
struct MainData
    main_number::Union{Int, Nothing}
    manche_pb_number::Union{Int, Nothing}
    index1::Union{Int, String}
    index2::String
    index3::String
    index5::String
end

"""
    FormulasTheoretical{T<:AbstractFloat}

Structure contenant les probabilités théoriques INDEX5 et paramètres pour les calculs d'entropie.
"""
struct FormulasTheoretical{T<:AbstractFloat}
    base::T
    epsilon::T
    theoretical_probs::Dict{String,T}

    function FormulasTheoretical{T}(base::T = T(2.0), epsilon::T = T(1e-12)) where T<:AbstractFloat
        # Probabilités théoriques INDEX5 (identiques à entropie_baccarat_analyzer.jl)
        theoretical_probs = Dict{String,T}(
            "0_A_BANKER" => T(0.085136), "1_A_BANKER" => T(0.086389),
            "0_B_BANKER" => T(0.064676), "1_B_BANKER" => T(0.065479),
            "0_C_BANKER" => T(0.077903), "1_C_BANKER" => T(0.078929),
            "0_A_PLAYER" => T(0.085240), "1_A_PLAYER" => T(0.086361),
            "0_B_PLAYER" => T(0.076907), "1_B_PLAYER" => T(0.077888),
            "0_C_PLAYER" => T(0.059617), "1_C_PLAYER" => T(0.060352),
            "0_A_TIE" => T(0.017719), "1_A_TIE" => T(0.017978),
            "0_B_TIE" => T(0.016281), "1_B_TIE" => T(0.016482),
            "0_C_TIE" => T(0.013241), "1_C_TIE" => T(0.013423)
        )
        new{T}(base, epsilon, theoretical_probs)
    end
end

# Constructeur de convenance
FormulasTheoretical(base::Real = 2.0, epsilon::Real = 1e-12) =
    FormulasTheoretical{Float64}(Float64(base), Float64(epsilon))

"""
    MetriquesTheorique{T<:AbstractFloat}

Structure pour stocker les 12 métriques théoriques calculées.
"""
struct MetriquesTheorique{T<:AbstractFloat}
    shannon_t::T      # ShannonT
    aep_t::T          # AEPT
    taux_t::T         # TauxT
    metric_t::T       # MetricT
    cond_t::T         # CondT
    divkl_t::T        # DivKLT
    infomut_t::T      # InfoMutT
    cross_t::T        # CrossT
    topo_t::T         # TopoT
    block_t::T        # BlockT
    conddec_t::T      # CondDecT
    theoaep_t::T      # TheoAEPT
end

"""
    DifferentielsPredictifs{T<:AbstractFloat}

Structure pour stocker les différentiels des 12 métriques théoriques pour la prédiction.
Calcule |métrique(n+1) - métrique(n)| pour chacune des 6 possibilités à la main n+1.
"""
struct DifferentielsPredictifs{T<:AbstractFloat}
    # Différentiels des 12 métriques théoriques
    diff_shannon_t::T      # |ShannonT(n+1) - ShannonT(n)|
    diff_aep_t::T          # |AEPT(n+1) - AEPT(n)|
    diff_taux_t::T         # |TauxT(n+1) - TauxT(n)|
    diff_metric_t::T       # |MetricT(n+1) - MetricT(n)|
    diff_cond_t::T         # |CondT(n+1) - CondT(n)|
    diff_divkl_t::T        # |DivKLT(n+1) - DivKLT(n)|
    diff_infomut_t::T      # |InfoMutT(n+1) - InfoMutT(n)|
    diff_cross_t::T        # |CrossT(n+1) - CrossT(n)|
    diff_topo_t::T         # |TopoT(n+1) - TopoT(n)|
    diff_block_t::T        # |BlockT(n+1) - BlockT(n)|
    diff_conddec_t::T      # |CondDecT(n+1) - CondDecT(n)|
    diff_theoaep_t::T      # |TheoAEPT(n+1) - TheoAEPT(n)|
end

"""
    CalculateurDifferentielsPredictifs{T<:AbstractFloat}

Classe pour calculer les différentiels prédictifs des métriques théoriques.
"""
struct CalculateurDifferentielsPredictifs{T<:AbstractFloat}
    formulas::FormulasTheoretical{T}

    function CalculateurDifferentielsPredictifs{T}(base::T = T(2.0), epsilon::T = T(1e-12)) where T<:AbstractFloat
        formulas = FormulasTheoretical{T}(base, epsilon)
        new{T}(formulas)
    end
end

# Constructeur de convenance
CalculateurDifferentielsPredictifs(base::Real = 2.0, epsilon::Real = 1e-12) =
    CalculateurDifferentielsPredictifs{Float64}(Float64(base), Float64(epsilon))

"""
    PredictionResult

Structure pour stocker le résultat de prédiction pour une main avec métriques et différentiels.
"""
struct PredictionResult
    main_actuelle::Int
    index5_actuel::String
    index1_suivant::Int
    index5_observe::Union{String, Nothing}  # INDEX5 réellement observé à la main n+1
    valeurs_possibles::Vector{String}
    metriques_par_possibilite::Vector{MetriquesTheorique{Float64}}
    differentiels_par_possibilite::Vector{DifferentielsPredictifs{Float64}}
end

# ═══════════════════════════════════════════════════════════════════════════════
# FONCTIONS UTILITAIRES
# ═══════════════════════════════════════════════════════════════════════════════

"""
    trouver_fichier_json_recent(dossier::String) -> String

Trouve le fichier JSON le plus récent dans le dossier spécifié.
"""
function trouver_fichier_json_recent(dossier::String)
    if !isdir(dossier)
        throw(ArgumentError("Le dossier '$dossier' n'existe pas"))
    end
    
    fichiers_json = filter(f -> endswith(f, ".json"), readdir(dossier))
    
    if isempty(fichiers_json)
        throw(ArgumentError("Aucun fichier JSON trouvé dans le dossier '$dossier'"))
    end
    
    # Trier par date de modification (plus récent en premier)
    fichiers_avec_dates = [(f, stat(joinpath(dossier, f)).mtime) for f in fichiers_json]
    sort!(fichiers_avec_dates, by=x->x[2], rev=true)
    
    fichier_recent = fichiers_avec_dates[1][1]
    chemin_complet = joinpath(dossier, fichier_recent)
    
    println("📁 Fichier JSON le plus récent trouvé : $fichier_recent")
    return chemin_complet
end

"""
    charger_donnees_partie(chemin_fichier::String, numero_partie::Int) -> Vector{MainData}

Charge les données d'une partie spécifique depuis un fichier JSON.
"""
function charger_donnees_partie(chemin_fichier::String, numero_partie::Int)
    println("📖 Chargement du fichier : $chemin_fichier")
    println("🎯 Recherche de la partie numéro : $numero_partie")

    try
        contenu = read(chemin_fichier, String)
        donnees = JSON.parse(contenu)

        if !haskey(donnees, "parties_condensees") || isempty(donnees["parties_condensees"])
            throw(ArgumentError("Format de fichier invalide : 'parties_condensees' manquant ou vide"))
        end

        # Chercher la partie demandée
        partie_trouvee = nothing
        for partie in donnees["parties_condensees"]
            if partie["partie_number"] == numero_partie
                partie_trouvee = partie
                break
            end
        end

        if partie_trouvee === nothing
            throw(ArgumentError("Partie numéro $numero_partie non trouvée"))
        end

        mains_data = partie_trouvee["mains_condensees"]

        # Convertir en structures MainData
        mains = MainData[]

        for main_data in mains_data
            # Ignorer les mains avec des données manquantes
            if main_data["main_number"] === nothing ||
               main_data["index1"] === "" ||
               main_data["index2"] === "" ||
               main_data["index3"] === ""
                continue
            end

            push!(mains, MainData(
                main_data["main_number"],
                main_data["manche_pb_number"],
                main_data["index1"],
                main_data["index2"],
                main_data["index3"],
                main_data["index5"]
            ))
        end

        println("✅ Partie $numero_partie chargée : $(length(mains)) mains valides")
        println("📊 Statistiques de la partie :")
        if haskey(partie_trouvee, "statistiques")
            stats = partie_trouvee["statistiques"]
            println("   • Total mains : $(stats["total_mains"])")
            println("   • Manches P/B : $(stats["total_manches_pb"])")
            println("   • Ties : $(stats["total_ties"])")
        end

        return mains

    catch e
        println("❌ Erreur lors du chargement : $e")
        rethrow(e)
    end
end

"""
    compter_parties_disponibles(chemin_fichier::String) -> Int

Compte le nombre de parties disponibles dans le fichier JSON.
"""
function compter_parties_disponibles(chemin_fichier::String)
    try
        contenu = read(chemin_fichier, String)
        donnees = JSON.parse(contenu)

        if !haskey(donnees, "parties_condensees")
            return 0
        end

        return length(donnees["parties_condensees"])

    catch e
        println("❌ Erreur lors du comptage des parties : $e")
        return 0
    end
end

# ═══════════════════════════════════════════════════════════════════════════════
# CALCULS DES MÉTRIQUES THÉORIQUES
# ═══════════════════════════════════════════════════════════════════════════════

"""
    calculer_formule1B_shannon_jointe_theo(formulas::FormulasTheoretical{T}, sequence::Vector{String}, n::Int) where T -> T

FORMULE 1B : Entropie de Shannon Jointe (VERSION THÉORIQUE)
Calcule l'entropie jointe théorique pour la fenêtre croissante de la main 1 à la main n.
"""
function calculer_formule1B_shannon_jointe_theo(
    formulas::FormulasTheoretical{T},
    sequence::Vector{String},
    n::Int
) where T<:AbstractFloat
    if n <= 0 || n > length(sequence)
        return zero(T)
    end

    # Extraire la sous-séquence jusqu'à la main n
    subsequence = sequence[1:n]

    # Compter les occurrences observées dans la séquence
    counts = Dict{String, Int}()
    for value in subsequence
        counts[value] = get(counts, value, 0) + 1
    end

    # Calculer l'entropie de Shannon théorique basée sur les probabilités INDEX5
    entropy = zero(T)
    total = length(subsequence)

    # Pour chaque valeur unique observée, utiliser sa probabilité théorique INDEX5
    for (value, count) in counts
        p_theo = get(formulas.theoretical_probs, value, formulas.epsilon)
        if p_theo > zero(T)
            # Entropie de Shannon : -∑ p_theo(x) log₂ p_theo(x)
            # Pondérée par la fréquence d'apparition dans la séquence
            weight = T(count) / T(total)
            entropy -= weight * p_theo * (log(p_theo) / log(formulas.base))
        end
    end

    return entropy
end

"""
    calculer_formule2B_aep_theo(formulas::FormulasTheoretical{T}, sequence::Vector{String}, n::Int) where T -> T

FORMULE 2B : Entropie par Symbole AEP (VERSION THÉORIQUE)
Calcule l'entropie moyenne par symbole selon l'AEP avec probabilités théoriques INDEX5.
"""
function calculer_formule2B_aep_theo(
    formulas::FormulasTheoretical{T},
    sequence::Vector{String},
    n::Int
) where T<:AbstractFloat
    if n <= 0 || n > length(sequence)
        return zero(T)
    end

    # Extraire la sous-séquence jusqu'à la main n
    subsequence = sequence[1:n]

    # Calculer -(1/n) ∑ᵢ₌₁ⁿ log₂ p_theo(xᵢ)
    total_log_prob = zero(T)

    for value in subsequence
        p_theo = get(formulas.theoretical_probs, value, formulas.epsilon)
        if p_theo > zero(T)
            total_log_prob -= log(p_theo) / log(formulas.base)
        end
    end

    return total_log_prob / T(n)
end

"""
    calculer_formule3B_taux_entropie_theo(formulas::FormulasTheoretical{T}, sequence::Vector{String}, n::Int) where T -> T

FORMULE 3B : Taux d'Entropie (VERSION THÉORIQUE)
Estime le taux d'entropie théorique comme limite asymptotique.
"""
function calculer_formule3B_taux_entropie_theo(
    formulas::FormulasTheoretical{T},
    sequence::Vector{String},
    n::Int
) where T<:AbstractFloat
    if n <= 0 || n > length(sequence)
        return zero(T)
    end

    # Calculer l'entropie jointe théorique
    h_joint = calculer_formule1B_shannon_jointe_theo(formulas, sequence, n)

    # Estimer le taux d'entropie : (1/n) × H_theo(X₁, ..., Xₙ)
    return h_joint / n
end

"""
    calculer_formule4B_entropie_metrique_theo(formulas::FormulasTheoretical{T}, sequence::Vector{String}, n::Int) where T -> T

FORMULE 4B : Entropie Métrique Kolmogorov-Sinai (VERSION THÉORIQUE)
Approximation pratique de l'entropie métrique théorique.
"""
function calculer_formule4B_entropie_metrique_theo(
    formulas::FormulasTheoretical{T},
    sequence::Vector{String},
    n::Int
) where T<:AbstractFloat
    if n <= 0 || n > length(sequence)
        return zero(T)
    end

    # Approximation : h_metric ≈ H(n)/n pour la partition naturelle INDEX5
    h_joint = calculer_formule1B_shannon_jointe_theo(formulas, sequence, n)
    return h_joint / n
end

"""
    calculer_formule5B_conditionnelle_theo(formulas::FormulasTheoretical{T}, sequence::Vector{String}, n::Int) where T -> T

FORMULE 5B : Entropie Conditionnelle Cumulative (VERSION THÉORIQUE)
Calcule l'information théorique apportée par le n-ème symbole.
"""
function calculer_formule5B_conditionnelle_theo(
    formulas::FormulasTheoretical{T},
    sequence::Vector{String},
    n::Int
) where T<:AbstractFloat
    if n <= 1 || n > length(sequence)
        return zero(T)
    end

    # H_theo(X₁, ..., Xₙ)
    h_n = calculer_formule1B_shannon_jointe_theo(formulas, sequence, n)

    # H_theo(X₁, ..., Xₙ₋₁)
    h_n_minus_1 = calculer_formule1B_shannon_jointe_theo(formulas, sequence, n-1)

    # Entropie conditionnelle
    return h_n - h_n_minus_1
end

"""
    calculer_formule6B_divergence_kl_theo(formulas::FormulasTheoretical{T}, sequence::Vector{String}, n::Int) where T -> T

FORMULE 6B : Entropie Relative - Divergence KL (VERSION THÉORIQUE)
Mesure la divergence entre distribution théorique INDEX5 et uniforme.
"""
function calculer_formule6B_divergence_kl_theo(
    formulas::FormulasTheoretical{T},
    sequence::Vector{String},
    n::Int
) where T<:AbstractFloat
    if n <= 0 || n > length(sequence)
        return zero(T)
    end

    # Extraire la sous-séquence jusqu'à la main n
    subsequence = sequence[1:n]

    # Compter les occurrences observées
    counts = Dict{String, Int}()
    for value in subsequence
        counts[value] = get(counts, value, 0) + 1
    end

    # Calculer la divergence KL : ∑ p_theo(x) log₂(p_theo(x)/p_unif(x))
    divergence = zero(T)
    p_unif = T(1.0 / 18.0)  # Distribution uniforme sur 18 valeurs INDEX5

    for (value, count) in counts
        p_theo = get(formulas.theoretical_probs, value, formulas.epsilon)
        if p_theo > zero(T) && p_unif > zero(T)
            divergence += p_theo * (log(p_theo / p_unif) / log(formulas.base))
        end
    end

    return divergence
end

"""
    calculer_formule7B_information_mutuelle_theo(formulas::FormulasTheoretical{T}, sequence::Vector{String}, n::Int) where T -> T

FORMULE 7B : Information Mutuelle Cumulative (VERSION THÉORIQUE)
Mesure la dépendance théorique entre X_t et X_{t+1} selon le modèle INDEX5.
"""
function calculer_formule7B_information_mutuelle_theo(
    formulas::FormulasTheoretical{T},
    sequence::Vector{String},
    n::Int
) where T<:AbstractFloat
    if n <= 1 || n > length(sequence)
        return zero(T)
    end

    # Extraire la sous-séquence jusqu'à la main n
    subsequence = sequence[1:n]

    # Créer les paires (x_t, x_{t+1})
    pairs = [(subsequence[i], subsequence[i+1]) for i in 1:length(subsequence)-1]

    if isempty(pairs)
        return zero(T)
    end

    # Compter les occurrences des paires jointes
    joint_counts = Dict{Tuple{String,String}, Int}()
    for pair in pairs
        joint_counts[pair] = get(joint_counts, pair, 0) + 1
    end

    # Compter les occurrences marginales
    x_counts = Dict{String, Int}()
    y_counts = Dict{String, Int}()
    for (x, y) in pairs
        x_counts[x] = get(x_counts, x, 0) + 1
        y_counts[y] = get(y_counts, y, 0) + 1
    end

    # Calculer l'information mutuelle
    mutual_info = zero(T)
    total_pairs = length(pairs)

    for ((x, y), joint_count) in joint_counts
        p_x_theo = get(formulas.theoretical_probs, x, formulas.epsilon)
        p_y_theo = get(formulas.theoretical_probs, y, formulas.epsilon)
        p_xy = T(joint_count) / T(total_pairs)  # Fréquence observée de la paire

        if p_xy > zero(T) && p_x_theo > zero(T) && p_y_theo > zero(T)
            mutual_info += p_xy * (log(p_xy / (p_x_theo * p_y_theo)) / log(formulas.base))
        end
    end

    return mutual_info
end

"""
    calculer_formule8B_entropie_croisee_theo(formulas::FormulasTheoretical{T}, sequence::Vector{String}, n::Int) where T -> T

FORMULE 8B : Entropie Croisée Cumulative (VERSION THÉORIQUE)
Mesure le coût d'encodage avec distribution uniforme vs théorique INDEX5.
"""
function calculer_formule8B_entropie_croisee_theo(
    formulas::FormulasTheoretical{T},
    sequence::Vector{String},
    n::Int
) where T<:AbstractFloat
    if n <= 0 || n > length(sequence)
        return zero(T)
    end

    # Extraire la sous-séquence jusqu'à la main n
    subsequence = sequence[1:n]

    # Compter les occurrences observées
    counts = Dict{String, Int}()
    for value in subsequence
        counts[value] = get(counts, value, 0) + 1
    end

    # Calculer l'entropie croisée : -∑ p_theo(x) log₂ q_unif(x)
    cross_entropy = zero(T)
    q_unif = T(1.0 / 18.0)  # Distribution uniforme de référence

    for (value, count) in counts
        p_theo = get(formulas.theoretical_probs, value, formulas.epsilon)
        if p_theo > zero(T) && q_unif > zero(T)
            cross_entropy -= p_theo * (log(q_unif) / log(formulas.base))
        end
    end

    return cross_entropy
end

"""
    calculer_formule9B_entropie_topologique_theo(formulas::FormulasTheoretical{T}, sequence::Vector{String}, n::Int) where T -> T

FORMULE 9B : Entropie Topologique Cumulative (VERSION THÉORIQUE)
Approximation théorique basée sur la complexité du modèle INDEX5.
"""
function calculer_formule9B_entropie_topologique_theo(
    formulas::FormulasTheoretical{T},
    sequence::Vector{String},
    n::Int
) where T<:AbstractFloat
    if n <= 0 || n > length(sequence)
        return zero(T)
    end

    # Extraire la sous-séquence jusqu'à la main n
    subsequence = sequence[1:n]

    # Calculer la complexité théorique basée sur les probabilités INDEX5
    unique_values = Set{String}()
    for value in subsequence
        push!(unique_values, value)
    end

    # Calculer la complexité topologique théorique
    complexity = zero(T)
    for value in unique_values
        p_theo = get(formulas.theoretical_probs, value, formulas.epsilon)
        if p_theo > zero(T)
            # Contribution à la complexité : p_theo × log₂(1/p_theo)
            complexity += p_theo * (log(T(1.0) / p_theo) / log(formulas.base))
        end
    end

    # Normaliser par la longueur
    return complexity / T(n)
end

"""
    calculer_formule10B_block_cumulative_theo(formulas::FormulasTheoretical{T}, sequence::Vector{String}, n::Int) where T -> T

FORMULE 10B : Entropie de Block Cumulative (VERSION THÉORIQUE)
Identique à l'entropie jointe de Shannon théorique.
"""
function calculer_formule10B_block_cumulative_theo(
    formulas::FormulasTheoretical{T},
    sequence::Vector{String},
    n::Int
) where T<:AbstractFloat
    # Cette formule est identique à la formule 1B (Shannon Jointe théorique)
    return calculer_formule1B_shannon_jointe_theo(formulas, sequence, n)
end

"""
    calculer_formule11B_conditionnelle_decroissante_theo(formulas::FormulasTheoretical{T}, sequence::Vector{String}, n::Int) where T -> T

FORMULE 11B : Entropie Conditionnelle Décroissante (VERSION THÉORIQUE)
Identique à la formule 5B (Conditionnelle Cumulative théorique).
"""
function calculer_formule11B_conditionnelle_decroissante_theo(
    formulas::FormulasTheoretical{T},
    sequence::Vector{String},
    n::Int
) where T<:AbstractFloat
    # Cette formule est identique à la formule 5B (Conditionnelle théorique)
    return calculer_formule5B_conditionnelle_theo(formulas, sequence, n)
end

"""
    calculer_formule12B_theoreme_aep_theo(formulas::FormulasTheoretical{T}, sequence::Vector{String}, n::Int) where T -> T

FORMULE 12B : Théorème AEP Shannon-McMillan-Breiman (VERSION THÉORIQUE)
Convergence vers le taux d'entropie théorique selon le théorème AEP.
"""
function calculer_formule12B_theoreme_aep_theo(
    formulas::FormulasTheoretical{T},
    sequence::Vector{String},
    n::Int
) where T<:AbstractFloat
    # Cette formule est équivalente à la formule 2B (AEP théorique)
    return calculer_formule2B_aep_theo(formulas, sequence, n)
end

"""
    calculer_toutes_metriques_theoriques(formulas::FormulasTheoretical{T}, sequence::Vector{String}, n::Int) where T -> MetriquesTheorique{T}

Calcule toutes les 12 métriques théoriques pour une séquence donnée jusqu'à la main n.
"""
function calculer_toutes_metriques_theoriques(
    formulas::FormulasTheoretical{T},
    sequence::Vector{String},
    n::Int
) where T<:AbstractFloat

    shannon_t = calculer_formule1B_shannon_jointe_theo(formulas, sequence, n)
    aep_t = calculer_formule2B_aep_theo(formulas, sequence, n)
    taux_t = calculer_formule3B_taux_entropie_theo(formulas, sequence, n)
    metric_t = calculer_formule4B_entropie_metrique_theo(formulas, sequence, n)
    cond_t = calculer_formule5B_conditionnelle_theo(formulas, sequence, n)
    divkl_t = calculer_formule6B_divergence_kl_theo(formulas, sequence, n)
    infomut_t = calculer_formule7B_information_mutuelle_theo(formulas, sequence, n)
    cross_t = calculer_formule8B_entropie_croisee_theo(formulas, sequence, n)
    topo_t = calculer_formule9B_entropie_topologique_theo(formulas, sequence, n)
    block_t = calculer_formule10B_block_cumulative_theo(formulas, sequence, n)
    conddec_t = calculer_formule11B_conditionnelle_decroissante_theo(formulas, sequence, n)
    theoaep_t = calculer_formule12B_theoreme_aep_theo(formulas, sequence, n)

    return MetriquesTheorique{T}(
        shannon_t, aep_t, taux_t, metric_t, cond_t, divkl_t,
        infomut_t, cross_t, topo_t, block_t, conddec_t, theoaep_t
    )
end

# ═══════════════════════════════════════════════════════════════════════════════
# CALCUL DES DIFFÉRENTIELS PRÉDICTIFS
# ═══════════════════════════════════════════════════════════════════════════════

"""
    calculer_differentiels_predictifs(calculateur::CalculateurDifferentielsPredictifs{T}, metriques_n::MetriquesTheorique{T}, metriques_n_plus_1::MetriquesTheorique{T}) where T -> DifferentielsPredictifs{T}

Calcule les différentiels prédictifs entre les métriques de la main n et n+1.
Formule : |métrique(n+1) - métrique(n)| pour chacune des 12 métriques théoriques.
"""
function calculer_differentiels_predictifs(
    calculateur::CalculateurDifferentielsPredictifs{T},
    metriques_n::MetriquesTheorique{T},
    metriques_n_plus_1::MetriquesTheorique{T}
) where T<:AbstractFloat

    # Calculer les différentiels absolus |métrique(n+1) - métrique(n)|
    diff_shannon_t = abs(metriques_n_plus_1.shannon_t - metriques_n.shannon_t)
    diff_aep_t = abs(metriques_n_plus_1.aep_t - metriques_n.aep_t)
    diff_taux_t = abs(metriques_n_plus_1.taux_t - metriques_n.taux_t)
    diff_metric_t = abs(metriques_n_plus_1.metric_t - metriques_n.metric_t)
    diff_cond_t = abs(metriques_n_plus_1.cond_t - metriques_n.cond_t)
    diff_divkl_t = abs(metriques_n_plus_1.divkl_t - metriques_n.divkl_t)
    diff_infomut_t = abs(metriques_n_plus_1.infomut_t - metriques_n.infomut_t)
    diff_cross_t = abs(metriques_n_plus_1.cross_t - metriques_n.cross_t)
    diff_topo_t = abs(metriques_n_plus_1.topo_t - metriques_n.topo_t)
    diff_block_t = abs(metriques_n_plus_1.block_t - metriques_n.block_t)
    diff_conddec_t = abs(metriques_n_plus_1.conddec_t - metriques_n.conddec_t)
    diff_theoaep_t = abs(metriques_n_plus_1.theoaep_t - metriques_n.theoaep_t)

    return DifferentielsPredictifs{T}(
        diff_shannon_t, diff_aep_t, diff_taux_t, diff_metric_t,
        diff_cond_t, diff_divkl_t, diff_infomut_t, diff_cross_t,
        diff_topo_t, diff_block_t, diff_conddec_t, diff_theoaep_t
    )
end

"""
    calculer_differentiels_pour_possibilites(calculateur::CalculateurDifferentielsPredictifs{T}, sequence_n::Vector{String}, valeurs_possibles::Vector{String}) where T -> Vector{DifferentielsPredictifs{T}}

Calcule les différentiels prédictifs pour toutes les possibilités à la main n+1.
"""
function calculer_differentiels_pour_possibilites(
    calculateur::CalculateurDifferentielsPredictifs{T},
    sequence_n::Vector{String},
    valeurs_possibles::Vector{String}
) where T<:AbstractFloat

    n = length(sequence_n)

    # Calculer les métriques pour la main n (état actuel)
    metriques_n = calculer_toutes_metriques_theoriques(calculateur.formulas, sequence_n, n)

    # Calculer les différentiels pour chaque possibilité
    differentiels = DifferentielsPredictifs{T}[]

    for valeur_possible in valeurs_possibles
        # Créer la séquence hypothétique avec cette valeur ajoutée
        sequence_n_plus_1 = vcat(sequence_n, [valeur_possible])

        # Calculer les métriques pour la main n+1 (état hypothétique)
        metriques_n_plus_1 = calculer_toutes_metriques_theoriques(calculateur.formulas, sequence_n_plus_1, n + 1)

        # Calculer les différentiels |métrique(n+1) - métrique(n)|
        diff = calculer_differentiels_predictifs(calculateur, metriques_n, metriques_n_plus_1)
        push!(differentiels, diff)
    end

    return differentiels
end

# ═══════════════════════════════════════════════════════════════════════════════
# LOGIQUE DE PRÉDICTION
# ═══════════════════════════════════════════════════════════════════════════════

"""
    calculer_index1_suivant(index1_actuel::Int, index2_actuel::String) -> Int

Calcule la valeur d'INDEX1 pour la main suivante selon les règles de transition.
"""
function calculer_index1_suivant(index1_actuel::Int, index2_actuel::String)
    if index2_actuel == "C"
        # Règle C : INDEX1 s'inverse
        return index1_actuel == 0 ? 1 : 0
    elseif index2_actuel == "A" || index2_actuel == "B"
        # Règles A et B : INDEX1 reste identique
        return index1_actuel
    else
        throw(ArgumentError("INDEX2 invalide : '$index2_actuel'. Doit être A, B ou C"))
    end
end

"""
    generer_valeurs_possibles(index1_suivant::Int) -> Vector{String}

Génère les 6 valeurs possibles d'INDEX5 pour la main suivante.
Exclut les possibilités TIE à l'INDEX3.
Ordre : d'abord tous les BANKER (A, B, C), puis tous les PLAYER (A, B, C).
"""
function generer_valeurs_possibles(index1_suivant::Int)
    index2_possibles = ["A", "B", "C"]
    index3_possibles = ["BANKER", "PLAYER"]  # TIE supprimé

    valeurs_possibles = String[]

    # D'abord tous les BANKER : A, B, C
    for index2 in index2_possibles
        index5 = "$(index1_suivant)_$(index2)_BANKER"
        push!(valeurs_possibles, index5)
    end

    # Puis tous les PLAYER : A, B, C
    for index2 in index2_possibles
        index5 = "$(index1_suivant)_$(index2)_PLAYER"
        push!(valeurs_possibles, index5)
    end

    return valeurs_possibles
end

"""
    predire_main_suivante(main_actuelle::MainData, sequence_jusqu_n::Vector{String}, index5_observe::Union{String, Nothing} = nothing) -> PredictionResult

Prédit les valeurs possibles pour la main suivante avec calcul des métriques théoriques.
"""
function predire_main_suivante(main_actuelle::MainData, sequence_jusqu_n::Vector{String}, index5_observe::Union{String, Nothing} = nothing)
    # Calculer INDEX1 pour la main suivante
    index1_suivant = calculer_index1_suivant(main_actuelle.index1, main_actuelle.index2)

    # Générer toutes les valeurs possibles
    valeurs_possibles = generer_valeurs_possibles(index1_suivant)

    # Initialiser les structures pour les calculs d'entropie et différentiels
    formulas = FormulasTheoretical{Float64}()
    calculateur_diff = CalculateurDifferentielsPredictifs{Float64}()

    # Calculer les métriques pour la main n (état actuel)
    n = length(sequence_jusqu_n)
    metriques_n = calculer_toutes_metriques_theoriques(formulas, sequence_jusqu_n, n)

    # Calculer les métriques et différentiels pour chaque possibilité
    metriques_par_possibilite = MetriquesTheorique{Float64}[]
    differentiels_par_possibilite = DifferentielsPredictifs{Float64}[]

    for valeur_possible in valeurs_possibles
        # Créer une séquence hypothétique avec cette valeur ajoutée
        sequence_hypothetique = vcat(sequence_jusqu_n, [valeur_possible])
        n_hypothetique = length(sequence_hypothetique)

        # Calculer toutes les métriques théoriques pour cette séquence hypothétique (main n+1)
        metriques_n_plus_1 = calculer_toutes_metriques_theoriques(formulas, sequence_hypothetique, n_hypothetique)
        push!(metriques_par_possibilite, metriques_n_plus_1)

        # Calculer les différentiels |métrique(n+1) - métrique(n)|
        diff = calculer_differentiels_predictifs(calculateur_diff, metriques_n, metriques_n_plus_1)
        push!(differentiels_par_possibilite, diff)
    end

    return PredictionResult(
        main_actuelle.main_number,
        main_actuelle.index5,
        index1_suivant,
        index5_observe,
        valeurs_possibles,
        metriques_par_possibilite,
        differentiels_par_possibilite
    )
end

# ═══════════════════════════════════════════════════════════════════════════════
# AFFICHAGE ET INTERFACE
# ═══════════════════════════════════════════════════════════════════════════════

"""
    afficher_prediction(prediction::PredictionResult, compact::Bool=false, avec_metriques::Bool=false)

Affiche le résultat de prédiction de manière formatée.
"""
function afficher_prediction(prediction::PredictionResult, compact::Bool=false, avec_metriques::Bool=false)
    if compact && !avec_metriques
        # Affichage compact pour les listes longues
        println("\n📊 Main $(prediction.main_actuelle) → Main $(prediction.main_actuelle + 1)")
        println("   INDEX5 actuel: $(prediction.index5_actuel) | INDEX1 suivant: $(prediction.index1_suivant)")
        print("   Valeurs possibles: ")
        println(join(prediction.valeurs_possibles, " "))
    elseif avec_metriques
        # Affichage avec métriques théoriques
        println("\n" * "="^100)
        println("🎯 PRÉDICTION AVEC MÉTRIQUES POUR LA MAIN $(prediction.main_actuelle + 1)")
        println("="^100)
        println("📊 Main actuelle : $(prediction.main_actuelle)")
        println("🎲 INDEX5 actuel : $(prediction.index5_actuel)")
        println("🔄 INDEX1 suivant : $(prediction.index1_suivant)")

        # Afficher l'INDEX5 observé (toujours afficher la ligne)
        if prediction.index5_observe !== nothing
            println("👁️  INDEX5 observé : $(prediction.index5_observe)")
        else
            println("👁️  INDEX5 observé : (non disponible)")
        end

        println("\n📋 Les 6 valeurs possibles avec leurs métriques théoriques et différentiels :")
        println("-"^200)

        # En-tête du tableau avec métriques et différentiels selon la hiérarchie d'importance
        println(@sprintf("%-15s %8s | %4s %8s | %4s %8s | %4s %8s | %4s %8s | %4s %8s | %4s %8s | %4s %8s | %4s %8s | %4s %8s | %4s %8s | %4s %8s | %4s",
            "INDEX5", "CondT", "Diff", "CondDecT", "Diff", "InfoMutT", "Diff", "TheoAEPT", "Diff", "TauxT", "Diff", "MetricT", "Diff",
            "CrossT", "Diff", "TopoT", "Diff", "BlockT", "Diff", "DivKLT", "Diff", "ShannonT", "Diff", "AEPT", "Diff"))
        println("-"^200)

        for (i, (valeur, metriques, differentiels)) in enumerate(zip(prediction.valeurs_possibles, prediction.metriques_par_possibilite, prediction.differentiels_par_possibilite))
            println(@sprintf("%-15s %8.4f | %4.4f %8.4f | %4.4f %8.4f | %4.4f %8.4f | %4.4f %8.4f | %4.4f %8.4f | %4.4f %8.4f | %4.4f %8.4f | %4.4f %8.4f | %4.4f %8.4f | %4.4f %8.4f | %4.4f %8.4f | %4.4f",
                valeur,
                metriques.cond_t, differentiels.diff_cond_t,
                metriques.conddec_t, differentiels.diff_conddec_t,
                metriques.infomut_t, differentiels.diff_infomut_t,
                metriques.theoaep_t, differentiels.diff_theoaep_t,
                metriques.taux_t, differentiels.diff_taux_t,
                metriques.metric_t, differentiels.diff_metric_t,
                metriques.cross_t, differentiels.diff_cross_t,
                metriques.topo_t, differentiels.diff_topo_t,
                metriques.block_t, differentiels.diff_block_t,
                metriques.divkl_t, differentiels.diff_divkl_t,
                metriques.shannon_t, differentiels.diff_shannon_t,
                metriques.aep_t, differentiels.diff_aep_t))
        end

        println("-"^200)
        println("✨ Total : $(length(prediction.valeurs_possibles)) valeurs possibles avec métriques et différentiels calculés")
    else
        # Affichage détaillé standard
        println("\n" * "="^80)
        println("🎯 PRÉDICTION POUR LA MAIN $(prediction.main_actuelle + 1)")
        println("="^80)
        println("📊 Main actuelle : $(prediction.main_actuelle)")
        println("🎲 INDEX5 actuel : $(prediction.index5_actuel)")
        println("🔄 INDEX1 suivant : $(prediction.index1_suivant)")
        println("\n📋 Les 6 valeurs possibles d'INDEX5 pour la main $(prediction.main_actuelle + 1) :")
        println("-"^50)

        for (i, valeur) in enumerate(prediction.valeurs_possibles)
            println(@sprintf("%2d. %s", i, valeur))
        end

        println("-"^50)
        println("✨ Total : $(length(prediction.valeurs_possibles)) valeurs possibles")
    end
end

"""
    afficher_regles()

Affiche les règles de transition d'INDEX1.
"""
function afficher_regles()
    println("\n📜 RÈGLES DE TRANSITION INDEX1 :")
    println("="^50)
    println("• Si INDEX2 = C → INDEX1 s'inverse (0→1, 1→0)")
    println("• Si INDEX2 = A → INDEX1 reste identique")
    println("• Si INDEX2 = B → INDEX1 reste identique")
    println("="^50)
end

# ═══════════════════════════════════════════════════════════════════════════════
# PROGRAMME PRINCIPAL
# ═══════════════════════════════════════════════════════════════════════════════

"""
    main()

Fonction principale du programme.
"""
function main()
    println("🚀 PRÉDICTEUR INDEX5 - DÉMARRAGE")
    println("="^60)
    
    try
        # 1. Charger automatiquement le fichier JSON le plus récent
        dossier_partie = "partie"
        chemin_fichier = trouver_fichier_json_recent(dossier_partie)

        # 2. Compter les parties disponibles
        nb_parties = compter_parties_disponibles(chemin_fichier)
        println("📊 Nombre de parties disponibles : $nb_parties")

        if nb_parties == 0
            println("❌ Aucune partie trouvée dans le fichier")
            return
        end

        # 3. Demander à l'utilisateur de choisir une partie
        println("\n🎯 SÉLECTION DE LA PARTIE")
        println("="^50)

        numero_partie = 0
        while true
            print("➤ Choisissez le numéro de partie (1-$nb_parties) : ")
            input = strip(readline())

            try
                numero_partie = parse(Int, input)
                if numero_partie >= 1 && numero_partie <= nb_parties
                    break
                else
                    println("❌ Numéro invalide. Doit être entre 1 et $nb_parties")
                end
            catch
                println("❌ Veuillez entrer un numéro valide")
            end
        end

        # 4. Charger la partie sélectionnée
        mains = charger_donnees_partie(chemin_fichier, numero_partie)

        if isempty(mains)
            println("❌ Aucune main valide trouvée dans la partie $numero_partie")
            return
        end

        # 5. Afficher les règles
        afficher_regles()

        # 6. Demander le mode d'affichage
        println("\n🎯 MODE D'AFFICHAGE")
        println("="^50)
        println("1. Compact (sans métriques)")
        println("2. Détaillé avec métriques théoriques")

        mode_affichage = 1
        while true
            print("➤ Choisissez le mode d'affichage (1-2) : ")
            input = strip(readline())

            try
                mode_affichage = parse(Int, input)
                if mode_affichage >= 1 && mode_affichage <= 2
                    break
                else
                    println("❌ Mode invalide. Doit être 1 ou 2")
                end
            catch
                println("❌ Veuillez entrer un numéro valide")
            end
        end

        avec_metriques = (mode_affichage == 2)

        # 7. Générer les prédictions pour toutes les mains 1 à 59
        println("\n🎯 GÉNÉRATION DES PRÉDICTIONS POUR LA PARTIE $numero_partie (mains 1 à 59)")
        println("="^80)

        # Calculer le nombre maximum de mains à traiter (59 ou moins si le fichier en contient moins)
        max_mains = min(59, length(mains))

        println("📊 Traitement de $max_mains mains...")
        if avec_metriques
            println("⚡ Calcul des 12 métriques théoriques pour chaque possibilité...")
        end

        # Construire la séquence INDEX5 pour les calculs d'entropie
        sequence_index5 = [main.index5 for main in mains]

        # Générer toutes les prédictions
        predictions = PredictionResult[]

        for i in 1:max_mains
            main_actuelle = mains[i]
            # Utiliser la séquence jusqu'à la main actuelle pour les calculs d'entropie
            sequence_jusqu_n = sequence_index5[1:i]

            # Récupérer l'INDEX5 observé à la main n+1 (si disponible)
            index5_observe = nothing
            if i < length(mains)  # S'il y a une main suivante
                index5_observe = mains[i + 1].index5
            end

            if avec_metriques
                prediction = predire_main_suivante(main_actuelle, sequence_jusqu_n, index5_observe)
            else
                # Version simplifiée sans métriques pour mode compact
                index1_suivant = calculer_index1_suivant(main_actuelle.index1, main_actuelle.index2)
                valeurs_possibles = generer_valeurs_possibles(index1_suivant)
                prediction = PredictionResult(
                    main_actuelle.main_number,
                    main_actuelle.index5,
                    index1_suivant,
                    index5_observe,
                    valeurs_possibles,
                    MetriquesTheorique{Float64}[],  # Vide pour mode compact
                    DifferentielsPredictifs{Float64}[]  # Vide pour mode compact
                )
            end

            push!(predictions, prediction)
        end

        # Afficher toutes les prédictions
        for prediction in predictions
            afficher_prediction(prediction, !avec_metriques, avec_metriques)
        end

        # Résumé final
        println("\n" * "="^80)
        println("✅ RÉSUMÉ FINAL - PARTIE $numero_partie")
        println("="^80)
        println("📈 Nombre total de prédictions générées : $(length(predictions))")
        println("🎲 Chaque prédiction contient 6 valeurs possibles d'INDEX5 (TIE exclu)")
        if avec_metriques
            println("📊 12 métriques théoriques calculées pour chaque possibilité")
            println("📋 Total de calculs de métriques : $(length(predictions) * 6 * 12)")
        else
            println("📋 Total de valeurs possibles générées : $(length(predictions) * 6)")
        end
        println("="^80)
        
    catch e
        println("💥 Erreur fatale : $e")
        return 1
    end
    
    return 0
end

# Lancer le programme si exécuté directement
if abspath(PROGRAM_FILE) == @__FILE__
    exit(main())
end
