"""
PRÉDICTEUR INDEX5 - DÉTERMINATION DES VALEURS POSSIBLES À LA MAIN N+1
=====================================================================

Programme qui détermine les 9 valeurs possibles d'INDEX5 à la main n+1
basé sur les règles de transition d'INDEX1 selon INDEX2.

RÈGLES DE TRANSITION INDEX1 :
- Si INDEX1 = 0 et INDEX2 = C à la main n → INDEX1 = 1 à la main n+1
- Si INDEX1 = 1 et INDEX2 = C à la main n → INDEX1 = 0 à la main n+1
- Si INDEX1 = 0 et INDEX2 = A à la main n → INDEX1 = 0 à la main n+1
- Si INDEX1 = 1 et INDEX2 = A à la main n → INDEX1 = 1 à la main n+1
- Si INDEX1 = 0 et INDEX2 = B à la main n → INDEX1 = 0 à la main n+1
- Si INDEX1 = 1 et INDEX2 = B à la main n → INDEX1 = 1 à la main n+1

INDEX5 = INDEX1_INDEX2_INDEX3
"""

using JSON
using Printf
using Dates

# ═══════════════════════════════════════════════════════════════════════════════
# STRUCTURES ET TYPES
# ═══════════════════════════════════════════════════════════════════════════════

"""
    MainData

Structure pour stocker les données d'une main.
"""
struct MainData
    main_number::Union{Int, Nothing}
    manche_pb_number::Union{Int, Nothing}
    index1::Union{Int, String}
    index2::String
    index3::String
    index5::String
end

"""
    PredictionResult

Structure pour stocker le résultat de prédiction pour une main.
"""
struct PredictionResult
    main_actuelle::Int
    index5_actuel::String
    index1_suivant::Int
    valeurs_possibles::Vector{String}
end

# ═══════════════════════════════════════════════════════════════════════════════
# FONCTIONS UTILITAIRES
# ═══════════════════════════════════════════════════════════════════════════════

"""
    trouver_fichier_json_recent(dossier::String) -> String

Trouve le fichier JSON le plus récent dans le dossier spécifié.
"""
function trouver_fichier_json_recent(dossier::String)
    if !isdir(dossier)
        throw(ArgumentError("Le dossier '$dossier' n'existe pas"))
    end
    
    fichiers_json = filter(f -> endswith(f, ".json"), readdir(dossier))
    
    if isempty(fichiers_json)
        throw(ArgumentError("Aucun fichier JSON trouvé dans le dossier '$dossier'"))
    end
    
    # Trier par date de modification (plus récent en premier)
    fichiers_avec_dates = [(f, stat(joinpath(dossier, f)).mtime) for f in fichiers_json]
    sort!(fichiers_avec_dates, by=x->x[2], rev=true)
    
    fichier_recent = fichiers_avec_dates[1][1]
    chemin_complet = joinpath(dossier, fichier_recent)
    
    println("📁 Fichier JSON le plus récent trouvé : $fichier_recent")
    return chemin_complet
end

"""
    charger_donnees_partie(chemin_fichier::String) -> Vector{MainData}

Charge les données de partie depuis un fichier JSON.
"""
function charger_donnees_partie(chemin_fichier::String)
    println("📖 Chargement du fichier : $chemin_fichier")
    
    try
        contenu = read(chemin_fichier, String)
        donnees = JSON.parse(contenu)
        
        if !haskey(donnees, "parties_condensees") || isempty(donnees["parties_condensees"])
            throw(ArgumentError("Format de fichier invalide : 'parties_condensees' manquant ou vide"))
        end
        
        # Prendre la première partie
        partie = donnees["parties_condensees"][1]
        mains_data = partie["mains_condensees"]
        
        # Convertir en structures MainData
        mains = MainData[]
        
        for main_data in mains_data
            # Ignorer les mains avec des données manquantes
            if main_data["main_number"] === nothing || 
               main_data["index1"] === "" || 
               main_data["index2"] === "" || 
               main_data["index3"] === ""
                continue
            end
            
            push!(mains, MainData(
                main_data["main_number"],
                main_data["manche_pb_number"],
                main_data["index1"],
                main_data["index2"],
                main_data["index3"],
                main_data["index5"]
            ))
        end
        
        println("✅ Données chargées : $(length(mains)) mains valides")
        return mains
        
    catch e
        println("❌ Erreur lors du chargement : $e")
        rethrow(e)
    end
end

# ═══════════════════════════════════════════════════════════════════════════════
# LOGIQUE DE PRÉDICTION
# ═══════════════════════════════════════════════════════════════════════════════

"""
    calculer_index1_suivant(index1_actuel::Int, index2_actuel::String) -> Int

Calcule la valeur d'INDEX1 pour la main suivante selon les règles de transition.
"""
function calculer_index1_suivant(index1_actuel::Int, index2_actuel::String)
    if index2_actuel == "C"
        # Règle C : INDEX1 s'inverse
        return index1_actuel == 0 ? 1 : 0
    elseif index2_actuel == "A" || index2_actuel == "B"
        # Règles A et B : INDEX1 reste identique
        return index1_actuel
    else
        throw(ArgumentError("INDEX2 invalide : '$index2_actuel'. Doit être A, B ou C"))
    end
end

"""
    generer_valeurs_possibles(index1_suivant::Int) -> Vector{String}

Génère les 9 valeurs possibles d'INDEX5 pour la main suivante.
"""
function generer_valeurs_possibles(index1_suivant::Int)
    index2_possibles = ["A", "B", "C"]
    index3_possibles = ["BANKER", "PLAYER", "TIE"]
    
    valeurs_possibles = String[]
    
    for index2 in index2_possibles
        for index3 in index3_possibles
            index5 = "$(index1_suivant)_$(index2)_$(index3)"
            push!(valeurs_possibles, index5)
        end
    end
    
    return valeurs_possibles
end

"""
    predire_main_suivante(main_actuelle::MainData) -> PredictionResult

Prédit les valeurs possibles pour la main suivante.
"""
function predire_main_suivante(main_actuelle::MainData)
    # Calculer INDEX1 pour la main suivante
    index1_suivant = calculer_index1_suivant(main_actuelle.index1, main_actuelle.index2)
    
    # Générer toutes les valeurs possibles
    valeurs_possibles = generer_valeurs_possibles(index1_suivant)
    
    return PredictionResult(
        main_actuelle.main_number,
        main_actuelle.index5,
        index1_suivant,
        valeurs_possibles
    )
end

# ═══════════════════════════════════════════════════════════════════════════════
# AFFICHAGE ET INTERFACE
# ═══════════════════════════════════════════════════════════════════════════════

"""
    afficher_prediction(prediction::PredictionResult, compact::Bool=false)

Affiche le résultat de prédiction de manière formatée.
"""
function afficher_prediction(prediction::PredictionResult, compact::Bool=false)
    if compact
        # Affichage compact pour les listes longues
        println("\n📊 Main $(prediction.main_actuelle) → Main $(prediction.main_actuelle + 1)")
        println("   INDEX5 actuel: $(prediction.index5_actuel) | INDEX1 suivant: $(prediction.index1_suivant)")
        print("   Valeurs possibles: ")
        println(join(prediction.valeurs_possibles, ", "))
    else
        # Affichage détaillé
        println("\n" * "="^80)
        println("🎯 PRÉDICTION POUR LA MAIN $(prediction.main_actuelle + 1)")
        println("="^80)
        println("📊 Main actuelle : $(prediction.main_actuelle)")
        println("🎲 INDEX5 actuel : $(prediction.index5_actuel)")
        println("🔄 INDEX1 suivant : $(prediction.index1_suivant)")
        println("\n📋 Les 9 valeurs possibles d'INDEX5 pour la main $(prediction.main_actuelle + 1) :")
        println("-"^50)

        for (i, valeur) in enumerate(prediction.valeurs_possibles)
            println(@sprintf("%2d. %s", i, valeur))
        end

        println("-"^50)
        println("✨ Total : $(length(prediction.valeurs_possibles)) valeurs possibles")
    end
end

"""
    afficher_regles()

Affiche les règles de transition d'INDEX1.
"""
function afficher_regles()
    println("\n📜 RÈGLES DE TRANSITION INDEX1 :")
    println("="^50)
    println("• Si INDEX2 = C → INDEX1 s'inverse (0→1, 1→0)")
    println("• Si INDEX2 = A → INDEX1 reste identique")
    println("• Si INDEX2 = B → INDEX1 reste identique")
    println("="^50)
end

# ═══════════════════════════════════════════════════════════════════════════════
# PROGRAMME PRINCIPAL
# ═══════════════════════════════════════════════════════════════════════════════

"""
    main()

Fonction principale du programme.
"""
function main()
    println("🚀 PRÉDICTEUR INDEX5 - DÉMARRAGE")
    println("="^60)
    
    try
        # 1. Charger automatiquement le fichier JSON le plus récent
        dossier_partie = "partie"
        chemin_fichier = trouver_fichier_json_recent(dossier_partie)
        mains = charger_donnees_partie(chemin_fichier)
        
        if isempty(mains)
            println("❌ Aucune main valide trouvée dans le fichier")
            return
        end
        
        # 2. Afficher les règles
        afficher_regles()
        
        # 3. Générer les prédictions pour toutes les mains 1 à 59
        println("\n🎯 GÉNÉRATION DES PRÉDICTIONS POUR TOUTES LES MAINS (1 à 59)")
        println("="^80)

        # Calculer le nombre maximum de mains à traiter (59 ou moins si le fichier en contient moins)
        max_mains = min(59, length(mains))

        println("📊 Traitement de $max_mains mains...")

        # Générer toutes les prédictions
        predictions = PredictionResult[]

        for i in 1:max_mains
            main_actuelle = mains[i]
            prediction = predire_main_suivante(main_actuelle)
            push!(predictions, prediction)
        end

        # Afficher toutes les prédictions en mode compact
        for prediction in predictions
            afficher_prediction(prediction, true)  # Mode compact
        end

        # Résumé final
        println("\n" * "="^80)
        println("✅ RÉSUMÉ FINAL")
        println("="^80)
        println("📈 Nombre total de prédictions générées : $(length(predictions))")
        println("🎲 Chaque prédiction contient 9 valeurs possibles d'INDEX5")
        println("📋 Total de valeurs possibles générées : $(length(predictions) * 9)")
        println("="^80)
        
    catch e
        println("💥 Erreur fatale : $e")
        return 1
    end
    
    return 0
end

# Lancer le programme si exécuté directement
if abspath(PROGRAM_FILE) == @__FILE__
    exit(main())
end
