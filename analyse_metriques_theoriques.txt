ANALYSE COMPLÈTE DES 12 MÉTRIQUES THÉORIQUES
=====================================================

Basé sur l'analyse complète de strat.txt et entropie_baccarat_analyzer.jl

STRATÉGIE GÉNÉRALE (selon strat.txt) :
=====================================

La stratégie de prédiction de la main n+1 utilise 5 phases :

Phase 1 : Évaluation de la Prévisibilité du Système
- Analyser CondT (Prédictibilité théorique de la main n selon INDEX5)
- Confirmer avec CondDecT (Amélioration théorique de la prédiction INDEX5)

Phase 2 : Mesure de la Complexité du Système  
- Évaluer TauxT (Complexité théorique intrinsèque du système INDEX5)
- Analyser MetricT (Complexité dynamique théorique du modèle INDEX5)
- Analyser TopoT (Complexité géométrique théorique INDEX5)

Phase 3 : Détection des Patterns et Corrélations
- Identifier InfoMutT (Corrélation théorique entre patterns INDEX5)
- Mesurer CrossT (Avantage informationnel du modèle INDEX5)

Phase 4 : Validation de la Convergence
- Vérifier TheoAEPT (Convergence vers l'entropie théorique INDEX5)
- Contrôler BlockT (Croissance théorique de l'information INDEX5)

Phase 5 : Ajustement selon les Biais
- Corriger DivKLT (Biais intrinsèque du modèle INDEX5 par rapport à l'équiprobabilité)
- Calibrer ShannonT (Incertitude attendue selon les probabilités de référence INDEX5)
- Calibrer AEPT (Information théorique moyenne par main INDEX5)

DÉTAIL DES 12 MÉTRIQUES THÉORIQUES :
===================================

1. ShannonT - Entropie de Shannon Jointe (VERSION THÉORIQUE)
============================================================

FONCTION PRINCIPALE : calculer_formule1B_shannon_jointe_theo()
Lignes : 991-1026

FORMULE : H_theo(X₁, X₂, ..., Xₙ) = -∑ p_theo(x₁,...,xₙ) log₂ p_theo(x₁,...,xₙ)

DESCRIPTION :
- Calcule l'entropie jointe théorique pour la fenêtre croissante de la main 1 à la main n
- Utilise les probabilités théoriques INDEX5 du modèle
- Mesure l'incertitude attendue selon les probabilités de référence INDEX5

MÉTHODES IMPLIQUÉES :
- calculer_formule1B_shannon_jointe_theo() : Fonction principale
- FormulasTheoretical{T} : Structure contenant les probabilités théoriques INDEX5
- safe_log() : Calcul sécurisé du logarithme (lignes 279-286)

PROBABILITÉS THÉORIQUES INDEX5 UTILISÉES (lignes 251-261) :
"0_A_BANKER" => 0.085136, "1_A_BANKER" => 0.086389,
"0_B_BANKER" => 0.064676, "1_B_BANKER" => 0.065479,
"0_C_BANKER" => 0.077903, "1_C_BANKER" => 0.078929,
"0_A_PLAYER" => 0.085240, "1_A_PLAYER" => 0.086361,
"0_B_PLAYER" => 0.076907, "1_B_PLAYER" => 0.077888,
"0_C_PLAYER" => 0.059617, "1_C_PLAYER" => 0.060352,
"0_A_TIE" => 0.017719, "1_A_TIE" => 0.017978,
"0_B_TIE" => 0.016281, "1_B_TIE" => 0.016482,
"0_C_TIE" => 0.013241, "1_C_TIE" => 0.013423

ALGORITHME :
1. Extraire la sous-séquence jusqu'à la main n
2. Compter les occurrences observées dans la séquence
3. Pour chaque valeur unique observée, utiliser sa probabilité théorique INDEX5
4. Calculer l'entropie de Shannon : -∑ p_theo(x) log₂ p_theo(x)
5. Pondérer par la fréquence d'apparition dans la séquence

UTILISATION DANS LA STRATÉGIE :
- Phase 5 : Calibrer l'incertitude avec ShannonT
- Mesure l'incertitude attendue selon les probabilités de référence INDEX5

2. AEPT - Entropie par Symbole AEP (VERSION THÉORIQUE)
======================================================

FONCTION PRINCIPALE : calculer_formule2B_aep_theo()
Lignes : 1036-1064

FORMULE : H_AEP_theo(X₁ⁿ) = -(1/n) ∑ᵢ₌₁ⁿ log₂ p_theo(xᵢ)

DESCRIPTION :
- Calcule l'entropie moyenne par symbole selon l'AEP avec probabilités théoriques INDEX5
- Information théorique moyenne par main INDEX5
- Mesure la convergence vers l'entropie théorique selon le théorème AEP

MÉTHODES IMPLIQUÉES :
- calculer_formule2B_aep_theo() : Fonction principale
- FormulasTheoretical{T} : Structure avec probabilités théoriques
- Utilise les mêmes probabilités INDEX5 que ShannonT

ALGORITHME :
1. Extraire la sous-séquence jusqu'à la main n
2. Pour chaque valeur dans la séquence :
   - Récupérer p_theo depuis les probabilités INDEX5
   - Calculer -log₂(p_theo)
3. Faire la moyenne : (1/n) × ∑(-log₂(p_theo))

UTILISATION DANS LA STRATÉGIE :
- Phase 5 : Calibrer AEPT (Information théorique moyenne par main INDEX5)
- Mesure la convergence théorique attendue

3. TauxT - Taux d'Entropie (VERSION THÉORIQUE)
==============================================

FONCTION PRINCIPALE : calculer_formule3B_taux_entropie_theo()
Lignes : 1074-1088

FORMULE : H_theo(Ξ) = lim_{n→∞} (1/n) H_theo(X₁, X₂, ..., Xₙ)

DESCRIPTION :
- Estime le taux d'entropie théorique comme limite asymptotique
- Complexité théorique intrinsèque du système INDEX5
- Mesure la complexité fondamentale du modèle

MÉTHODES IMPLIQUÉES :
- calculer_formule3B_taux_entropie_theo() : Fonction principale
- calculer_formule1B_shannon_jointe_theo() : Appelée pour calculer H_joint
- FormulasTheoretical{T} : Structure avec probabilités théoriques

ALGORITHME :
1. Calculer l'entropie jointe théorique H_joint
2. Estimer le taux d'entropie : H_joint / n

UTILISATION DANS LA STRATÉGIE :
- Phase 2 : Évaluer TauxT (Complexité théorique intrinsèque du système INDEX5)
- Un taux faible suggère un système plus ordonné et donc plus prévisible

4. MetricT - Entropie Métrique Kolmogorov-Sinai (VERSION THÉORIQUE)
===================================================================

FONCTION PRINCIPALE : calculer_formule4B_entropie_metrique_theo()
Lignes : 1098-1110

FORMULE : h_μ_theo(T) = sup{h_μ_theo(T, α) : α partition finie}

DESCRIPTION :
- Approximation pratique de l'entropie métrique théorique
- Complexité dynamique théorique du modèle INDEX5
- Mesure la complexité du système dynamique

MÉTHODES IMPLIQUÉES :
- calculer_formule4B_entropie_metrique_theo() : Fonction principale
- calculer_formule1B_shannon_jointe_theo() : Appelée pour calculer H_joint
- FormulasTheoretical{T} : Structure avec probabilités théoriques

ALGORITHME :
1. Calculer l'entropie jointe théorique H_joint
2. Approximation : h_metric ≈ H_joint / n pour la partition naturelle INDEX5

UTILISATION DANS LA STRATÉGIE :
- Phase 2 : Analyser MetricT (Complexité dynamique théorique du modèle INDEX5)
- Des valeurs stables ou décroissantes indiquent un système qui se stabilise

5. CondT - Entropie Conditionnelle Cumulative (VERSION THÉORIQUE)
=================================================================

FONCTION PRINCIPALE : calculer_formule5B_conditionnelle_theo()
Lignes : 1120-1137

FORMULE : H_theo(Xₙ|X₁, ..., Xₙ₋₁) = H_theo(X₁, ..., Xₙ) - H_theo(X₁, ..., Xₙ₋₁)

DESCRIPTION :
- Calcule l'information théorique apportée par le n-ème symbole
- Prédictibilité théorique de la main n selon INDEX5
- Mesure l'incertitude résiduelle après observation de l'historique

MÉTHODES IMPLIQUÉES :
- calculer_formule5B_conditionnelle_theo() : Fonction principale
- calculer_formule1B_shannon_jointe_theo() : Appelée 2 fois (pour n et n-1)
- FormulasTheoretical{T} : Structure avec probabilités théoriques

ALGORITHME :
1. Calculer H_theo(X₁, ..., Xₙ)
2. Calculer H_theo(X₁, ..., Xₙ₋₁)
3. Entropie conditionnelle = H_n - H_n_minus_1

UTILISATION DANS LA STRATÉGIE :
- Phase 1 : Analyser CondT (Prédictibilité théorique de la main n selon INDEX5)
- Si cette valeur diminue, le système devient plus prévisible

6. DivKLT - Entropie Relative - Divergence KL (VERSION THÉORIQUE)
================================================================

FONCTION PRINCIPALE : calculer_formule6B_divergence_kl_theo()
Lignes : 1147-1177

FORMULE : D_KL_theo_unif(P_n||U_n) = ∑ p_theo(x) log₂(p_theo(x)/p_unif(x))

DESCRIPTION :
- Mesure la divergence entre distribution théorique INDEX5 et uniforme
- Biais intrinsèque du modèle INDEX5 par rapport à l'équiprobabilité
- Quantifie l'écart par rapport à une distribution uniforme

MÉTHODES IMPLIQUÉES :
- calculer_formule6B_divergence_kl_theo() : Fonction principale
- FormulasTheoretical{T} : Structure avec probabilités théoriques INDEX5
- Utilise p_unif = 1/18 comme distribution uniforme de référence

ALGORITHME :
1. Extraire la sous-séquence jusqu'à la main n
2. Pour chaque valeur unique observée :
   - p_theo = probabilité INDEX5
   - p_unif = 1/18 (distribution uniforme)
   - Calculer p_theo × log₂(p_theo/p_unif)
3. Sommer toutes les contributions

UTILISATION DANS LA STRATÉGIE :
- Phase 5 : Corriger DivKLT (Biais intrinsèque du modèle INDEX5)
- Identifier si le système favorise certains résultats

7. InfoMutT - Information Mutuelle Cumulative (VERSION THÉORIQUE)
================================================================

FONCTION PRINCIPALE : calculer_formule7B_information_mutuelle_theo()
Lignes : 1190-1254

FORMULE : I(X;Y) = ∑∑ p(x,y) log₂ (p(x,y)/(p(x)p(y)))

DESCRIPTION :
- Mesure la dépendance théorique entre X_t et X_{t+1} selon le modèle INDEX5
- Corrélation théorique entre patterns INDEX5
- Quantifie les dépendances temporelles dans le modèle

MÉTHODES IMPLIQUÉES :
- calculer_formule7B_information_mutuelle_theo() : Fonction principale
- FormulasTheoretical{T} : Structure avec probabilités théoriques INDEX5
- Calcul des paires (x_t, x_{t+1}) pour mesurer la dépendance temporelle

ALGORITHME :
1. Créer les paires (x_t, x_{t+1}) observées dans la séquence
2. Compter les occurrences des paires jointes
3. Compter les occurrences marginales
4. Pour chaque paire :
   - p_x_theo, p_y_theo = probabilités INDEX5 marginales
   - p_xy_theo = fréquence relative observée
   - Calculer p_xy_theo × log₂(p_xy_theo / (p_x_theo × p_y_theo))
5. Sommer toutes les contributions

UTILISATION DANS LA STRATÉGIE :
- Phase 3 : Identifier InfoMutT (Corrélation théorique entre patterns INDEX5)
- Une valeur élevée révèle des dépendances fortes facilitant la prédiction

8. CrossT - Entropie Croisée Cumulative (VERSION THÉORIQUE)
==========================================================

FONCTION PRINCIPALE : calculer_formule8B_entropie_croisee_theo()
Lignes : 1264-1298

FORMULE : H_cross_theo_unif(P_n, U_n) = -∑ p_theo(x) log₂ q_unif(x)

DESCRIPTION :
- Mesure le coût d'encodage avec distribution uniforme vs théorique INDEX5
- Avantage informationnel du modèle INDEX5
- Quantifie l'efficacité du modèle INDEX5 par rapport à l'uniforme

MÉTHODES IMPLIQUÉES :
- calculer_formule8B_entropie_croisee_theo() : Fonction principale
- FormulasTheoretical{T} : Structure avec probabilités théoriques INDEX5
- Utilise q_unif = 1/18 comme distribution uniforme de référence

ALGORITHME :
1. Extraire la sous-séquence jusqu'à la main n
2. Pour chaque valeur unique observée :
   - p_theo = probabilité INDEX5
   - q_unif = 1/18 (distribution uniforme)
   - Calculer -p_theo × log₂(q_unif)
3. Sommer toutes les contributions

UTILISATION DANS LA STRATÉGIE :
- Phase 3 : Mesurer CrossT (Avantage informationnel du modèle INDEX5)
- Plus cette valeur est élevée, plus le modèle INDEX5 est supérieur à l'aléatoire

9. TopoT - Entropie Topologique Cumulative (VERSION THÉORIQUE)
==============================================================

FONCTION PRINCIPALE : calculer_formule9B_entropie_topologique_theo()
Lignes : 1308-1342

FORMULE : h_top_theo(f) = lim_{n→∞} (1/n) log₂ |{orbites périodiques de période ≤ n}|

DESCRIPTION :
- Approximation théorique basée sur la complexité du modèle INDEX5
- Complexité géométrique théorique INDEX5
- Mesure la complexité topologique du système dynamique

MÉTHODES IMPLIQUÉES :
- calculer_formule9B_entropie_topologique_theo() : Fonction principale
- FormulasTheoretical{T} : Structure avec probabilités théoriques INDEX5

ALGORITHME :
1. Extraire la sous-séquence jusqu'à la main n
2. Identifier les valeurs uniques dans la séquence
3. Pour chaque valeur unique :
   - p_theo = probabilité INDEX5
   - Calculer p_theo × log₂(1/p_theo) (contribution à la complexité)
4. Normaliser par la longueur : complexity / n

UTILISATION DANS LA STRATÉGIE :
- Phase 2 : Analyser TopoT (Complexité géométrique théorique INDEX5)
- Des valeurs stables indiquent un système qui se stabilise dans des patterns

10. BlockT - Entropie de Block Cumulative (VERSION THÉORIQUE)
============================================================

FONCTION PRINCIPALE : calculer_formule10B_block_cumulative_theo()
Lignes : 1352-1359

FORMULE : H_n_theo = H_theo(X₁, X₂, ..., Xₙ)

DESCRIPTION :
- Identique à l'entropie jointe de Shannon théorique
- Croissance théorique de l'information INDEX5
- Mesure l'accumulation d'information dans le système

MÉTHODES IMPLIQUÉES :
- calculer_formule10B_block_cumulative_theo() : Fonction principale
- calculer_formule1B_shannon_jointe_theo() : Appelée directement (identique)
- FormulasTheoretical{T} : Structure avec probabilités théoriques INDEX5

ALGORITHME :
- Identique à ShannonT (formule 1B)

UTILISATION DANS LA STRATÉGIE :
- Phase 4 : Contrôler BlockT (Croissance théorique de l'information INDEX5)
- Une croissance régulière indique un système stable

11. CondDecT - Entropie Conditionnelle Décroissante (VERSION THÉORIQUE)
=======================================================================

FONCTION PRINCIPALE : calculer_formule11B_conditionnelle_decroissante_theo()
Lignes : 1369-1376

FORMULE : u_n_theo = H_theo(Xₙ|X₁, X₂, ..., Xₙ₋₁)

DESCRIPTION :
- Identique à la formule 5B (Conditionnelle Cumulative théorique)
- Amélioration théorique de la prédiction INDEX5
- Mesure la décroissance de l'incertitude avec l'historique

MÉTHODES IMPLIQUÉES :
- calculer_formule11B_conditionnelle_decroissante_theo() : Fonction principale
- calculer_formule5B_conditionnelle_theo() : Appelée directement (identique)
- Toutes les méthodes de CondT s'appliquent

ALGORITHME :
- Identique à CondT (formule 5B)

UTILISATION DANS LA STRATÉGIE :
- Phase 1 : Confirmer avec CondDecT (Amélioration théorique de la prédiction INDEX5)
- Une tendance décroissante confirme l'amélioration de la capacité prédictive

12. TheoAEPT - Théorème AEP Shannon-McMillan-Breiman (VERSION THÉORIQUE)
=======================================================================

FONCTION PRINCIPALE : calculer_formule12B_theoreme_aep_theo()
Lignes : 1386-1398

FORMULE : lim_{n→∞} -(1/n) log₂ p_theo(X₁ⁿ) = H_theo(Ξ)

DESCRIPTION :
- Convergence vers le taux d'entropie théorique selon le théorème AEP
- Convergence vers l'entropie théorique INDEX5
- Valide la convergence du modèle vers ses valeurs théoriques

MÉTHODES IMPLIQUÉES :
- calculer_formule12B_theoreme_aep_theo() : Fonction principale
- calculer_formule2B_aep_theo() : Appelée directement (équivalente)
- Toutes les méthodes d'AEPT s'appliquent

ALGORITHME :
- Identique à AEPT (formule 2B)

UTILISATION DANS LA STRATÉGIE :
- Phase 4 : Vérifier TheoAEPT (Convergence vers l'entropie théorique INDEX5)
- Si le système converge vers les valeurs théoriques, les prédictions INDEX5 deviennent fiables

MÉTHODES UTILITAIRES COMMUNES :
==============================

1. FormulasTheoretical{T} (lignes 234-268)
- Structure principale contenant les probabilités théoriques INDEX5
- Base logarithmique (défaut: 2.0 pour bits)
- Epsilon pour éviter log(0) (défaut: 1e-12)

2. safe_log() (lignes 279-286)
- Calcul sécurisé du logarithme avec gestion de log(0)
- ACTUELLEMENT DÉSACTIVÉE (retourne zero(T))

3. validate_probabilities() (lignes 289-311)
- Validation et normalisation des vecteurs de probabilités
- ACTUELLEMENT DÉSACTIVÉE

4. calculer_toutes_metriques() (lignes 855-896)
- Fonction qui calcule toutes les 24 métriques (12 théoriques + 12 observées)
- Utilisée pour les calculs de différentiels

5. DifferentialMetrics{T} (lignes 762-814)
- Structure pour stocker les différentiels de toutes les métriques
- Calcule |métrique(n) - métrique(n-1)| pour chaque métrique

INTÉGRATION DANS LE SYSTÈME :
============================

Les 12 métriques théoriques sont intégrées dans plusieurs fonctions :

1. calculer_toutes_metriques() (lignes 863-874)
- Calcule les 12 métriques théoriques pour une main donnée

2. calculer_differentiels_pour_main!() (lignes 918-930)
- Calcule les différentiels absolus des métriques théoriques

3. analyser_entropie_complete() (lignes 1446-1457)
- Intègre toutes les formules théoriques dans l'analyse complète

4. Affichage dans les tableaux (lignes 1636-1637, 1746-1747, 3766-3767, 3773-3774)
- Les métriques sont affichées avec leurs noms courts dans les rapports

CORRESPONDANCES ENTRE NOMS COURTS ET FONCTIONS :
===============================================

ShannonT  → calculer_formule1B_shannon_jointe_theo()
AEPT      → calculer_formule2B_aep_theo()
TauxT     → calculer_formule3B_taux_entropie_theo()
MetricT   → calculer_formule4B_entropie_metrique_theo()
CondT     → calculer_formule5B_conditionnelle_theo()
DivKLT    → calculer_formule6B_divergence_kl_theo()
InfoMutT  → calculer_formule7B_information_mutuelle_theo()
CrossT    → calculer_formule8B_entropie_croisee_theo()
TopoT     → calculer_formule9B_entropie_topologique_theo()
BlockT    → calculer_formule10B_block_cumulative_theo()
CondDecT  → calculer_formule11B_conditionnelle_decroissante_theo()
TheoAEPT  → calculer_formule12B_theoreme_aep_theo()

RELATIONS ENTRE LES MÉTRIQUES :
==============================

MÉTRIQUES IDENTIQUES :
- BlockT = ShannonT (même fonction calculer_formule1B_shannon_jointe_theo)
- CondDecT = CondT (même fonction calculer_formule5B_conditionnelle_theo)
- TheoAEPT = AEPT (même fonction calculer_formule2B_aep_theo)

MÉTRIQUES DÉRIVÉES :
- TauxT = ShannonT / n
- MetricT = ShannonT / n (approximation identique à TauxT)
- CondT = ShannonT(n) - ShannonT(n-1)

MÉTRIQUES BASÉES SUR LES PROBABILITÉS INDEX5 :
- ShannonT, AEPT, DivKLT, InfoMutT, CrossT, TopoT utilisent directement les probabilités INDEX5
- TauxT, MetricT, CondT, BlockT, CondDecT, TheoAEPT sont dérivées de ShannonT ou AEPT

STRATÉGIE DE PRÉDICTION FINALE (selon strat.txt) :
=================================================

CONDITIONS POUR PRÉDICTION FIABLE :
1. Si CondT et CondDecT sont faibles → Le système est prévisible
2. Si InfoMutT est élevé → Il existe des corrélations exploitables
3. Si TauxT et MetricT sont stables → Le système suit des patterns réguliers
4. Si TheoAEPT converge → Les prédictions INDEX5 sont fiables

ALORS : Prédire la main n+1 en utilisant la valeur INDEX5 qui :
- Minimise l'entropie conditionnelle (CondT)
- Maximise la corrélation avec l'historique (InfoMutT)
- Respecte les biais identifiés (DivKLT)
- S'aligne avec la convergence théorique (TheoAEPT)

SINON : Le système est trop chaotique pour une prédiction fiable

CONCLUSION :
===========

Les 12 métriques théoriques forment un système cohérent d'analyse de l'entropie basé sur le modèle INDEX5. Chaque métrique a un rôle spécifique dans la stratégie de prédiction, depuis l'évaluation de la prévisibilité jusqu'à la validation de la convergence. L'implémentation utilise les probabilités théoriques INDEX5 comme référence et applique les formules d'entropie de Shannon de manière rigoureuse.

NOTES IMPORTANTES :
==================

1. Les fonctions safe_log() et validate_probabilities() sont actuellement désactivées
2. Certaines métriques sont identiques (BlockT=ShannonT, CondDecT=CondT, TheoAEPT=AEPT)
3. Le système utilise une base logarithmique de 2.0 (bits) par défaut
4. Les probabilités INDEX5 sont normalisées et totalisent 1.0
5. Le système gère les cas limites avec epsilon = 1e-12 pour éviter log(0)
